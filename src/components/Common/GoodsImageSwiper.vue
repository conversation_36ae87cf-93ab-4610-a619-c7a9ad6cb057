<template>
  <div class="goods-image-swiper">
    <swiper class="goods-swiper" :loop="enableLoop" :style="{ height: containerHeight }" @swiper="onSwiper"
      @slideChange="onSlideChange">
      <swiper-slide v-for="(item, index) in imageList" :key="index" class="goods-slide">
        <div class="image-wrapper">
          <img :src="item.url" :alt="item.alt || `商品图片${index + 1}`" class="goods-image"
            @click="handleImageClick(item, index)" />
        </div>
      </swiper-slide>
    </swiper>

    <!-- 分页器 -->
    <div v-if="showPagination && imageList.length > 1" class="pagination-container">
      <div v-if="paginationType === 'dots'" class="dots-pagination">
        <span v-for="(_, index) in imageList" :key="index" class="pagination-dot"
          :class="{ active: currentIndex === index }" @click="slideTo(index)"></span>
      </div>
      <div v-else-if="paginationType === 'fraction'" class="fraction-pagination">
        <span class="fraction-text">{{ currentIndex + 1 }} / {{ imageList.length }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'

// Props
const props = defineProps({
  imageList: {
    type: Array,
    default: () => []
  },
  height: {
    type: [String, Number],
    default: '375px'
  },
  paginationType: {
    type: String,
    default: 'dots',
    validator: (value) => ['dots', 'fraction', 'none'].includes(value)
  },
  showPagination: {
    type: Boolean,
    default: true
  }
})

const enableLoop = computed(() => {
  return props.imageList.length > 1
})

// Emits
const emit = defineEmits(['imageClick'])

// 响应式数据
const currentIndex = ref(0)
const swiperInstance = ref(null)

// 计算容器高度
const containerHeight = computed(() => {
  return typeof props.height === 'number' ? `${props.height}px` : props.height
})

// 方法
const onSwiper = (swiper) => {
  swiperInstance.value = swiper
}

const onSlideChange = (swiper) => {
  currentIndex.value = swiper.activeIndex
}

const slideTo = (index) => {
  if (swiperInstance.value) {
    swiperInstance.value.slideTo(index)
  }
}

const handleImageClick = (item, index) => {
  emit('imageClick', { item, index })
}
</script>

<style scoped lang="less">
.goods-image-swiper {
  position: relative;
  width: 100%;
  background-color: #ffffff;

  .goods-swiper {
    width: 100%;
  }

  .goods-slide {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    overflow: hidden;
  }

  .goods-image {
    cursor: pointer;
    display: block;
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    transition: transform 0.3s ease;

    &:hover {
      @media (min-width: 768px) {
        transform: scale(1.05);
      }
    }
  }

  // 分页器容器
  .pagination-container {
    position: absolute;
    bottom: 12px;
    left: 0;
    right: 0;
    z-index: 10;
    pointer-events: none;
  }

  // 圆点分页器
  .dots-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 6px;
    pointer-events: auto;

    .pagination-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.3);
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background-color: #ff7a0a;
        transform: scale(1.3);
      }

      &:hover {
        background-color: rgba(255, 122, 10, 0.7);
      }
    }
  }

  // 分数分页器
  .fraction-pagination {
    position: absolute;
    bottom: 0;
    right: 12px;
    pointer-events: auto;

    .fraction-text {
      font-size: 13px;
      font-weight: 500;
      color: #ffffff;
      background: rgba(0, 0, 0, 0.6);
      padding: 6px 12px;
      border-radius: 16px;
      backdrop-filter: blur(8px);
    }
  }
}
</style>
